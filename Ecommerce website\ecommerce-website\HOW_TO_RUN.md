# ShopMart E-Commerce Website - How to Run

This document provides instructions on how to run the ShopMart e-commerce website and how to customize it with your own content and images.

## Running the Website

1. Open your terminal or command prompt
2. Navigate to the ecommerce-website directory:
   ```
   cd d:\HAMMAD\Ecommerce website\ecommerce-website
   ```
3. Install React Router DOM (if you haven't already):
   ```
   npm install react-router-dom
   ```
4. Start the development server:
   ```
   npm start
   ```

The application should automatically open in your default browser at http://localhost:3000. If it doesn't open automatically, you can manually navigate to that URL.

## Website Features

ShopMart includes the following e-commerce features:

1. **Beautiful UI Design**: Modern, responsive design with animations, hover effects, and attractive layouts
2. **Product Browsing**: Browse products by category, search for products, and filter by price
3. **Product Details**: View detailed product information, images, and related products
4. **Shopping Cart**: Add products to cart, update quantities, and remove items
5. **User Authentication**: Register, login, and manage user profile
6. **Checkout Process**: Multi-step checkout with shipping, payment, and order review
7. **Order Management**: View order history and track orders
8. **About, Categories, and Contact Pages**: Dedicated pages with attractive layouts and content
9. **Social Media Integration**: Links to Facebook, WhatsApp, LinkedIn, and Instagram

## Adding Your Own Images

### Required Images

Place the following images in the `public/images` folder:

1. **Product Images**: `product-1.jpg` through `product-8.jpg`
2. **Category Images**:
   - `electronics.jpg`
   - `clothing.jpg`
   - `footwear.jpg`
   - `home.jpg`
   - `accessories.jpg`
   - `beauty.jpg`
3. **Banner Images**:
   - `hero-banner.jpg` (homepage hero section)
   - `special-offer.jpg` (special offers section)
   - `new-arrivals.jpg` (new arrivals section)
4. **Other Images**:
   - `about-us.jpg` (About page)
   - `customer-1.jpg`, `customer-2.jpg`, `customer-3.jpg` (customer testimonials)
   - `visa.png`, `mastercard.png`, `paypal.png`, `amex.png` (payment method icons)

All images have fallback placeholders, so the website will look good even without adding your own images.

## Customizing Social Media Links

The footer includes links to the following social media platforms:

- Facebook: https://facebook.com (update with your actual Facebook page URL)
- WhatsApp: https://wa.me/1234567890 (update with your actual WhatsApp number)
- LinkedIn: https://linkedin.com (update with your actual LinkedIn profile URL)
- Instagram: https://instagram.com (update with your actual Instagram profile URL)

To update these links, edit the `Footer.js` file in the `src/components` folder.

## Demo Login

For testing the login functionality, you can use:

- Email: <EMAIL>
- Password: password

## Customizing Your Website

To customize your website further:

1. **Company Information**:

   - The company name "ShopMart" can be changed in the Navbar.js and Footer.js files
   - The About page content can be edited in src/pages/AboutPage.js
   - The footer description can be edited in src/components/Footer.js

2. **Product and Category Data**:

   - Edit product information in src/data/products.js
   - Edit category information in src/data/categories.js

3. **Styling and Design**:

   - The website uses Bootstrap 5 for styling
   - Custom CSS is included directly in the components
   - You can modify colors, fonts, and other design elements by editing the CSS

4. **Contact Information**:
   - Update the contact information in the Footer.js file
   - Update the contact form in the AboutPage.js file (Contact section)

## Additional Notes

- The website is fully responsive and works well on mobile devices
- All images have fallback placeholders in case the actual images are not found
- The website includes animations and hover effects for a better user experience
- The About, Categories, and Contact pages share the same component but display different content based on the URL
