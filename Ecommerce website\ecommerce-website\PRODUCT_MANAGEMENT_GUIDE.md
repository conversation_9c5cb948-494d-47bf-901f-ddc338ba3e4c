# ShopMart Product Management Guide

This guide explains how to add more products to your ShopMart e-commerce website and manage your product catalog.

## Adding New Products

### Step 1: Add Product Images

1. Place your product images in the appropriate folder:
   - For general product images: `src/images/product images/`
   - For category-specific images: `src/images/category images/`

2. Use a consistent naming convention:
   - For sequential numbering: `product-9.jpg`, `product-10.jpg`, etc.
   - For descriptive naming: `bluetooth-speaker.jpg`, `wireless-earbuds.jpg`, etc.

3. Image recommendations:
   - Size: 600x600 pixels (1:1 aspect ratio)
   - Format: JPG or PNG
   - File size: Less than 200KB for optimal performance
   - Background: White or transparent

### Step 2: Add Product Data

Open the `src/data/products.js` file and add new product entries following this template:

```javascript
{
  id: 9, // Use the next available ID
  name: "Your Product Name",
  price: 99.99, // Set your price
  description: "Detailed product description.",
  image: require("../images/product images/product-9.jpg"), // Path to your image
  category: "Electronics", // Choose from existing categories
  rating: 4.5, // Rating from 1 to 5
  reviews: 50, // Number of reviews
  inStock: true, // Availability
  features: [
    "Feature 1",
    "Feature 2",
    "Feature 3",
    "Feature 4",
    "Feature 5"
  ]
}
```

### Step 3: Verify Product Display

After adding new products:

1. Run the application: `npm start`
2. Check the Products page to ensure your new products appear
3. Verify that the product count on the Categories page is updated
4. Test the product detail page for each new product

## Managing Product Categories

### Adding a New Category

1. Add a category image to `src/images/category images/`
2. Open `src/data/categories.js` and add a new category entry:

```javascript
{
  id: 7, // Use the next available ID
  name: "Your Category Name",
  image: require("../images/category images/your-category.jpg"),
  description: "Description of your category"
}
```

### Assigning Products to Categories

When adding new products, make sure to assign them to the correct category by setting the `category` property to match exactly one of your category names.

## Product Features and Best Practices

### Product Information

For each product, provide:

- **Clear Name**: Concise but descriptive
- **Accurate Price**: Use decimal format (e.g., 99.99)
- **Detailed Description**: 1-2 sentences explaining the product
- **Specific Features**: 3-5 bullet points highlighting key features
- **Correct Category**: Must match one of your defined categories
- **Realistic Rating**: Based on a 5-star scale
- **Review Count**: A reasonable number based on product popularity

### Image Guidelines

- **Consistent Style**: Maintain a consistent look across product images
- **High Quality**: Clear, well-lit images that showcase the product
- **Multiple Views**: Consider adding multiple images for each product
- **Proper Sizing**: Ensure all images have the same dimensions
- **Optimization**: Compress images for faster loading

## Advanced Product Management

### Adding Product Variants

For products with multiple variants (colors, sizes, etc.):

1. Create a separate product entry for each major variant
2. Use a consistent naming pattern (e.g., "T-Shirt - Blue", "T-Shirt - Red")
3. Use different images for each variant
4. Consider adding a "relatedProducts" array to link variants

### Adding Product Discounts

To add discounted products:

1. Add an original price property:
```javascript
originalPrice: 129.99,
price: 99.99,
```

2. Calculate and display discount percentage in your components:
```javascript
const discountPercentage = Math.round(((product.originalPrice - product.price) / product.originalPrice) * 100);
```

## Troubleshooting

If products don't appear correctly:

1. Check that the image path is correct and the image exists
2. Verify that the category name matches exactly (case-sensitive)
3. Ensure each product has a unique ID
4. Check the browser console for any errors

By following this guide, you can easily add and manage products in your ShopMart e-commerce website.
