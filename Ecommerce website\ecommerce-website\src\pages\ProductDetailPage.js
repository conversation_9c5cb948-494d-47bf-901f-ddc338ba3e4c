import React, { useState, useContext, useEffect } from 'react';
import { useParams, Link } from 'react-router-dom';
import { CartContext } from '../context/CartContext';
import products from '../data/products';

const ProductDetailPage = () => {
  const { id } = useParams();
  const { addToCart } = useContext(CartContext);
  const [quantity, setQuantity] = useState(1);
  const [product, setProduct] = useState(null);
  const [relatedProducts, setRelatedProducts] = useState([]);

  useEffect(() => {
    // Find the product by id
    const productId = parseInt(id);
    const foundProduct = products.find(p => p.id === productId);

    if (foundProduct) {
      setProduct(foundProduct);

      // Find related products (same category, excluding current product)
      const related = products
        .filter(p => p.category === foundProduct.category && p.id !== productId)
        .slice(0, 4); // Limit to 4 related products

      setRelatedProducts(related);
    }
  }, [id]);

  const handleQuantityChange = (e) => {
    const value = parseInt(e.target.value);
    setQuantity(value > 0 ? value : 1);
  };

  const handleAddToCart = (e) => {
    if (product) {
      // Add the product to cart
      addToCart(product, quantity);

      // Store a reference to the button element
      const button = e.currentTarget;

      // Add animation class
      if (button && button.classList) {
        button.classList.add('add-to-cart-animation');

        // Remove animation class after animation completes
        setTimeout(() => {
          // Check if the element still exists in the DOM
          if (button && button.classList && document.body.contains(button)) {
            button.classList.remove('add-to-cart-animation');
          }
        }, 500);
      }

      // Show a success message
      alert(`${product.name} added to cart!`);
    }
  };

  if (!product) {
    return (
      <div className="container py-5 text-center">
        <h2>Product not found</h2>
        <Link to="/products" className="btn btn-primary mt-3">Back to Products</Link>
      </div>
    );
  }

  return (
    <div className="container py-5">
      {/* Animation styles */}
      <style jsx="true">{`
        .add-to-cart-animation {
          animation: pulse 0.5s ease;
        }
        @keyframes pulse {
          0% { transform: scale(1); }
          50% { transform: scale(1.2); }
          100% { transform: scale(1); }
        }
      `}</style>
      <div className="row mb-5">
        {/* Product Image */}
        <div className="col-md-5 mb-4">
          <div className="border rounded shadow-sm p-3 bg-white">
            <img
              src={product.image}
              alt={product.name}
              className="img-fluid rounded"
              style={{maxHeight: '400px', objectFit: 'contain', width: '100%'}}
              onError={(e) => e.target.src = `https://via.placeholder.com/600x600?text=${encodeURIComponent(product.name)}`}
            />
          </div>
        </div>

        {/* Product Details */}
        <div className="col-md-7">
          <h1 className="mb-3">{product.name}</h1>

          <div className="d-flex align-items-center mb-3">
            <div className="me-3">
              <span className="badge bg-primary">
                <i className="fas fa-star"></i> {product.rating}
              </span>
            </div>
            <span className="text-muted">{product.reviews} reviews</span>
          </div>

          <h2 className="text-primary mb-4">${product.price.toFixed(2)}</h2>

          <p className="mb-4">{product.description}</p>

          <div className="mb-4">
            <h5>Features:</h5>
            <ul>
              {product.features.map((feature, index) => (
                <li key={index}>{feature}</li>
              ))}
            </ul>
          </div>

          <div className="d-flex align-items-center mb-4">
            <div className="input-group me-3" style={{ maxWidth: '150px' }}>
              <button
                className="btn btn-outline-secondary"
                type="button"
                onClick={() => setQuantity(prev => prev > 1 ? prev - 1 : 1)}
              >
                <i className="fas fa-minus"></i>
              </button>
              <input
                type="number"
                className="form-control text-center"
                value={quantity}
                onChange={handleQuantityChange}
                min="1"
              />
              <button
                className="btn btn-outline-secondary"
                type="button"
                onClick={() => setQuantity(prev => prev + 1)}
              >
                <i className="fas fa-plus"></i>
              </button>
            </div>

            <div className="ms-2">
              {product.inStock ? (
                <span className="badge bg-success">In Stock</span>
              ) : (
                <span className="badge bg-danger">Out of Stock</span>
              )}
            </div>
          </div>

          <div className="d-flex">
            <button
              className="btn btn-primary me-2"
              onClick={(e) => handleAddToCart(e)}
              disabled={!product.inStock}
            >
              <i className="fas fa-shopping-cart me-2"></i>
              Add to Cart
            </button>
            <button className="btn btn-outline-danger">
              <i className="fas fa-heart"></i>
            </button>
          </div>
        </div>
      </div>

      {/* Related Products */}
      {relatedProducts.length > 0 && (
        <div>
          <h3 className="mb-4">Related Products</h3>
          <div className="row">
            {relatedProducts.map(relatedProduct => (
              <div key={relatedProduct.id} className="col-md-3 mb-4">
                <div className="card h-100">
                  <div style={{height: '180px', overflow: 'hidden'}}>
                    <img
                      src={relatedProduct.image}
                      className="card-img-top"
                      alt={relatedProduct.name}
                      style={{objectFit: 'cover', height: '100%', width: '100%', objectPosition: 'center'}}
                      onError={(e) => e.target.src = `https://via.placeholder.com/300x300?text=${encodeURIComponent(relatedProduct.name)}`}
                    />
                  </div>
                  <div className="card-body">
                    <h5 className="card-title">{relatedProduct.name}</h5>
                    <p className="card-text text-muted">${relatedProduct.price.toFixed(2)}</p>
                    <Link to={`/products/${relatedProduct.id}`} className="btn btn-outline-primary">View Details</Link>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default ProductDetailPage;
