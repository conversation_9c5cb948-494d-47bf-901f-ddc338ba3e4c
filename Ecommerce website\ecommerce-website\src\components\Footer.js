import React from 'react';
import { Link } from 'react-router-dom';

const Footer = () => {
  return (
    <footer className="bg-dark text-white pt-5 pb-4">
      <div className="container">
        <div className="row">
          <div className="col-md-3 col-sm-6 mb-4">
            <h5 className="text-primary mb-3">About Us</h5>
            <p className="text-light">ShopMart is your premier destination for quality products at affordable prices. We're committed to providing an exceptional shopping experience with carefully curated products and outstanding customer service.</p>
            <p className="text-light">Our mission is to make online shopping accessible, enjoyable, and reliable for everyone.</p>
          </div>
          <div className="col-md-3 col-sm-6 mb-4">
            <h5 className="text-primary mb-3">Quick Links</h5>
            <ul className="list-unstyled">
              <li className="mb-2"><Link to="/" className="text-decoration-none text-light"><i className="fas fa-angle-right me-2"></i>Home</Link></li>
              <li className="mb-2"><Link to="/products" className="text-decoration-none text-light"><i className="fas fa-angle-right me-2"></i>Products</Link></li>
              <li className="mb-2"><Link to="/categories" className="text-decoration-none text-light"><i className="fas fa-angle-right me-2"></i>Categories</Link></li>
              <li className="mb-2"><Link to="/about" className="text-decoration-none text-light"><i className="fas fa-angle-right me-2"></i>About Us</Link></li>
              <li className="mb-2"><Link to="/contact" className="text-decoration-none text-light"><i className="fas fa-angle-right me-2"></i>Contact Us</Link></li>
            </ul>
          </div>
          <div className="col-md-3 col-sm-6 mb-4">
            <h5 className="text-primary mb-3">Customer Service</h5>
            <ul className="list-unstyled">
              <li className="mb-2"><Link to="/faq" className="text-decoration-none text-light"><i className="fas fa-question-circle me-2"></i>FAQ</Link></li>
              <li className="mb-2"><Link to="/shipping" className="text-decoration-none text-light"><i className="fas fa-truck me-2"></i>Shipping Policy</Link></li>
              <li className="mb-2"><Link to="/returns" className="text-decoration-none text-light"><i className="fas fa-undo me-2"></i>Returns & Refunds</Link></li>
              <li className="mb-2"><Link to="/terms" className="text-decoration-none text-light"><i className="fas fa-file-contract me-2"></i>Terms & Conditions</Link></li>
              <li className="mb-2"><Link to="/privacy" className="text-decoration-none text-light"><i className="fas fa-shield-alt me-2"></i>Privacy Policy</Link></li>
            </ul>
            <p className="text-light mt-3">
              We're dedicated to providing exceptional customer service. Our team is available 24/7 to assist you with any questions or concerns.
            </p>
          </div>
          <div className="col-md-3 col-sm-6 mb-4">
            <h5 className="text-primary mb-3">Connect With Us</h5>
            <div className="d-flex gap-3 fs-4 mb-4">
              <a href="https://facebook.com" target="_blank" rel="noopener noreferrer" className="text-decoration-none text-white bg-primary p-2 rounded-circle"><i className="fab fa-facebook-f"></i></a>
              <a href="https://wa.me/923266396757" target="_blank" rel="noopener noreferrer" className="text-decoration-none text-white bg-success p-2 rounded-circle"><i className="fab fa-whatsapp"></i></a>
              <a href="https://linkedin.com" target="_blank" rel="noopener noreferrer" className="text-decoration-none text-white bg-info p-2 rounded-circle"><i className="fab fa-linkedin-in"></i></a>
              <a href="https://instagram.com" target="_blank" rel="noopener noreferrer" className="text-decoration-none text-white bg-danger p-2 rounded-circle"><i className="fab fa-instagram"></i></a>
            </div>
            <div className="mt-3">
              <h6 className="text-primary">Subscribe to our newsletter</h6>
              <p className="text-light small">Get the latest updates, deals and exclusive offers directly to your inbox!</p>
              <div className="input-group">
                <input
                  type="email"
                  className="form-control bg-dark text-light border-secondary"
                  placeholder="Your email"
                  style={{color: 'white !important'}}
                />
                <button className="btn btn-primary">Subscribe</button>
              </div>
            </div>
          </div>
        </div>
        <hr className="mt-4" />
        <div className="row">
          <div className="col-md-6">
            <p className="mb-0">© 2023 ShopMart. All rights reserved.</p>
          </div>
          <div className="col-md-6 text-md-end">
            <img src={require("../images/cards/visa.webp")} alt="Visa" className="me-2" style={{height: '30px'}} onError={(e) => e.target.src = 'https://via.placeholder.com/40x25?text=Visa'} />
            <img src={require("../images/cards/MasterCard.png")} alt="Mastercard" className="me-2" style={{height: '30px'}} onError={(e) => e.target.src = 'https://via.placeholder.com/40x25?text=MC'} />
            <img src={require("../images/cards/paypal.png")} alt="PayPal" className="me-2" style={{height: '30px'}} onError={(e) => e.target.src = 'https://via.placeholder.com/40x25?text=PayPal'} />
            <img src={require("../images/cards/amex.jpg")} alt="American Express" style={{height: '30px'}} onError={(e) => e.target.src = 'https://via.placeholder.com/40x25?text=Amex'} />
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
