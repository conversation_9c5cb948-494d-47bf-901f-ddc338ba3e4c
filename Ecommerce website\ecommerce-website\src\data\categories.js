// Sample categories for the e-commerce website
// Import placeholder images
import electronicsImage from '../images/product images/product-3.JPG'; // Changed to product-3 for electronics
import clothingImage from '../images/product images/product-5.JPG';
import footwearImage from '../images/product images/product-6.jpg';
import homeImage from '../images/product images/product-7.jpg';
import accessoriesImage from '../images/product images/product-8.jpg';
import beautyImage from '../images/category images/beauty.jpg'; // Using dedicated beauty category image

const categories = [
  {
    id: 1,
    name: "Electronics",
    image: electronicsImage,
    description: "Latest gadgets and electronic devices"
  },
  {
    id: 2,
    name: "Clothing",
    image: clothingImage,
    description: "Fashion apparel for men and women"
  },
  {
    id: 3,
    name: "Footwear",
    image: footwearImage,
    description: "Shoes, sneakers, and boots for all occasions"
  },
  {
    id: 4,
    name: "Home & Kitchen",
    image: homeImage,
    description: "Appliances and essentials for your home"
  },
  {
    id: 5,
    name: "Accessories",
    image: accessoriesImage,
    description: "Bags, watches, and other accessories"
  },
  {
    id: 6,
    name: "Beauty & Personal Care",
    image: beautyImage,
    description: "Skincare, makeup, and personal care products"
  }
];

export default categories;
