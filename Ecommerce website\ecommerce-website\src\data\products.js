// Sample product data for the e-commerce website
const products = [
  {
    id: 1,
    name: "Smartphone X",
    price: 799.99,
    description: "Latest smartphone with advanced features and high-resolution camera.",
    image: require("../images/product images/product-1.jpg"),
    category: "Electronics",
    rating: 4.5,
    reviews: 120,
    inStock: true,
    features: [
      "6.5-inch OLED display",
      "Triple camera system",
      "5G connectivity",
      "All-day battery life",
      "Water and dust resistant"
    ]
  },
  {
    id: 2,
    name: "Laptop Pro",
    price: 1299.99,
    description: "Powerful laptop for professionals and creative work.",
    image: require("../images/product images/product-2.jpg"),
    category: "Electronics",
    rating: 4.8,
    reviews: 85,
    inStock: true,
    features: [
      "15-inch Retina display",
      "16GB RAM",
      "512GB SSD storage",
      "10-hour battery life",
      "Backlit keyboard"
    ]
  },
  {
    id: 3,
    name: "Wireless Headphones",
    price: 199.99,
    description: "Premium wireless headphones with noise cancellation.",
    image: require("../images/product images/product-3.JPG"),
    category: "Electronics",
    rating: 4.6,
    reviews: 210,
    inStock: true,
    features: [
      "Active noise cancellation",
      "30-hour battery life",
      "Comfortable over-ear design",
      "High-quality sound",
      "Voice assistant support"
    ]
  },
  {
    id: 4,
    name: "Smart Watch",
    price: 249.99,
    description: "Feature-packed smartwatch for fitness and everyday use.",
    image: require("../images/product images/product-4.JPG"),
    category: "Electronics",
    rating: 4.3,
    reviews: 95,
    inStock: true,
    features: [
      "Heart rate monitoring",
      "GPS tracking",
      "Water resistant",
      "Sleep tracking",
      "7-day battery life"
    ]
  },
  {
    id: 5,
    name: "Designer T-Shirt",
    price: 49.99,
    description: "Premium cotton t-shirt with modern design.",
    image: require("../images/product images/product-5.JPG"),
    category: "Clothing",
    rating: 4.2,
    reviews: 65,
    inStock: true,
    features: [
      "100% organic cotton",
      "Comfortable fit",
      "Machine washable",
      "Available in multiple colors",
      "Sizes S to XXL"
    ]
  },
  {
    id: 6,
    name: "Running Shoes",
    price: 129.99,
    description: "Lightweight and comfortable running shoes for athletes.",
    image: require("../images/product images/product-6.jpg"),
    category: "Footwear",
    rating: 4.7,
    reviews: 180,
    inStock: true,
    features: [
      "Breathable mesh upper",
      "Responsive cushioning",
      "Durable rubber outsole",
      "Reflective details",
      "Available in multiple colors"
    ]
  },
  {
    id: 7,
    name: "Coffee Maker",
    price: 89.99,
    description: "Programmable coffee maker for perfect brewing every time.",
    image: require("../images/product images/product-7.jpg"),
    category: "Home & Kitchen",
    rating: 4.4,
    reviews: 75,
    inStock: true,
    features: [
      "12-cup capacity",
      "Programmable timer",
      "Auto shut-off",
      "Brew strength control",
      "Easy to clean"
    ]
  },
  {
    id: 8,
    name: "Backpack",
    price: 59.99,
    description: "Durable backpack with multiple compartments for everyday use.",
    image: require("../images/product images/product-8.jpg"),
    category: "Accessories",
    rating: 4.5,
    reviews: 110,
    inStock: true,
    features: [
      "Water-resistant material",
      "Laptop compartment",
      "Multiple pockets",
      "Padded shoulder straps",
      "Breathable back panel"
    ]
  },
  {
    id: 9,
    name: "Sports Sneakers",
    price: 89.99,
    description: "Comfortable sports sneakers perfect for casual wear and light exercise.",
    image: "https://via.placeholder.com/600x600/6A0DAD/FFFFFF?text=Sports+Sneakers",
    category: "Footwear",
    rating: 4.3,
    reviews: 78,
    inStock: true,
    features: [
      "Breathable mesh upper",
      "Cushioned insole",
      "Non-slip rubber outsole",
      "Lightweight design",
      "Available in multiple colors"
    ]
  },
  {
    id: 10,
    name: "Casual Loafers",
    price: 69.99,
    description: "Stylish and comfortable loafers for everyday casual wear.",
    image: "https://via.placeholder.com/600x600/6A0DAD/FFFFFF?text=Casual+Loafers",
    category: "Footwear",
    rating: 4.1,
    reviews: 45,
    inStock: true,
    features: [
      "Genuine leather upper",
      "Slip-on design",
      "Padded footbed",
      "Flexible sole",
      "Classic style"
    ]
  },
  {
    id: 11,
    name: "Hiking Boots",
    price: 149.99,
    description: "Durable hiking boots designed for rough terrain and outdoor adventures.",
    image: "https://via.placeholder.com/600x600/6A0DAD/FFFFFF?text=Hiking+Boots",
    category: "Footwear",
    rating: 4.7,
    reviews: 92,
    inStock: true,
    features: [
      "Waterproof construction",
      "Ankle support",
      "Rugged outsole for traction",
      "Cushioned midsole",
      "Breathable lining"
    ]
  },
  {
    id: 12,
    name: "Formal Dress Shoes",
    price: 119.99,
    description: "Elegant formal shoes perfect for business and special occasions.",
    image: "https://via.placeholder.com/600x600/6A0DAD/FFFFFF?text=Formal+Dress+Shoes",
    category: "Footwear",
    rating: 4.5,
    reviews: 63,
    inStock: true,
    features: [
      "Genuine leather construction",
      "Classic design",
      "Comfortable insole",
      "Durable outsole",
      "Available in black and brown"
    ]
  },
  {
    id: 13,
    name: "Denim Jeans",
    price: 59.99,
    description: "Classic denim jeans with a comfortable fit and durable construction.",
    image: "https://via.placeholder.com/600x600/4B0082/FFFFFF?text=Denim+Jeans",
    category: "Clothing",
    rating: 4.4,
    reviews: 87,
    inStock: true,
    features: [
      "100% cotton denim",
      "Classic 5-pocket design",
      "Straight leg fit",
      "Machine washable",
      "Available in multiple washes"
    ]
  },
  {
    id: 14,
    name: "Casual Hoodie",
    price: 45.99,
    description: "Comfortable hoodie perfect for casual wear and relaxation.",
    image: "https://via.placeholder.com/600x600/4B0082/FFFFFF?text=Casual+Hoodie",
    category: "Clothing",
    rating: 4.2,
    reviews: 56,
    inStock: true,
    features: [
      "Soft cotton blend",
      "Kangaroo pocket",
      "Adjustable hood",
      "Ribbed cuffs and hem",
      "Available in multiple colors"
    ]
  },
  {
    id: 15,
    name: "Blender",
    price: 79.99,
    description: "Powerful blender for smoothies, soups, and other culinary creations.",
    image: "https://via.placeholder.com/600x600/008000/FFFFFF?text=Blender",
    category: "Home & Kitchen",
    rating: 4.5,
    reviews: 68,
    inStock: true,
    features: [
      "700-watt motor",
      "Multiple speed settings",
      "Pulse function",
      "Dishwasher-safe parts",
      "BPA-free materials"
    ]
  },
  {
    id: 16,
    name: "Cookware Set",
    price: 129.99,
    description: "Complete cookware set with pots and pans for all your cooking needs.",
    image: "https://via.placeholder.com/600x600/008000/FFFFFF?text=Cookware+Set",
    category: "Home & Kitchen",
    rating: 4.6,
    reviews: 75,
    inStock: true,
    features: [
      "10-piece set",
      "Non-stick coating",
      "Heat-resistant handles",
      "Dishwasher safe",
      "Compatible with all stovetops"
    ]
  },
  {
    id: 17,
    name: "Leather Wallet",
    price: 29.99,
    description: "Genuine leather wallet with multiple card slots and compartments.",
    image: "https://via.placeholder.com/600x600/A0522D/FFFFFF?text=Leather+Wallet",
    category: "Accessories",
    rating: 4.4,
    reviews: 62,
    inStock: true,
    features: [
      "Genuine leather",
      "Multiple card slots",
      "Bill compartment",
      "ID window",
      "Slim design"
    ]
  },
  {
    id: 18,
    name: "Sunglasses",
    price: 39.99,
    description: "Stylish sunglasses with UV protection for sunny days.",
    image: "https://via.placeholder.com/600x600/A0522D/FFFFFF?text=Sunglasses",
    category: "Accessories",
    rating: 4.3,
    reviews: 48,
    inStock: true,
    features: [
      "UV400 protection",
      "Polarized lenses",
      "Durable frame",
      "Includes case",
      "Unisex design"
    ]
  },
  {
    id: 19,
    name: "Facial Cleanser",
    price: 24.99,
    description: "Gentle facial cleanser that removes impurities without drying the skin.",
    image: "https://via.placeholder.com/600x600/FF69B4/FFFFFF?text=Facial+Cleanser",
    category: "Beauty & Personal Care",
    rating: 4.4,
    reviews: 83,
    inStock: true,
    features: [
      "Suitable for all skin types",
      "Fragrance-free",
      "Removes makeup",
      "pH balanced",
      "Dermatologist tested"
    ]
  },
  {
    id: 20,
    name: "Moisturizer",
    price: 29.99,
    description: "Hydrating moisturizer that nourishes and protects the skin.",
    image: "https://via.placeholder.com/600x600/FF69B4/FFFFFF?text=Moisturizer",
    category: "Beauty & Personal Care",
    rating: 4.5,
    reviews: 76,
    inStock: true,
    features: [
      "24-hour hydration",
      "Non-greasy formula",
      "Contains SPF 15",
      "Anti-aging properties",
      "Suitable for daily use"
    ]
  },
  {
    id: 21,
    name: "Perfume",
    price: 59.99,
    description: "Elegant fragrance with long-lasting scent for any occasion.",
    image: "https://via.placeholder.com/600x600/FF69B4/FFFFFF?text=Perfume",
    category: "Beauty & Personal Care",
    rating: 4.6,
    reviews: 92,
    inStock: true,
    features: [
      "Eau de parfum",
      "50ml bottle",
      "Long-lasting scent",
      "Floral and fruity notes",
      "Elegant glass bottle"
    ]
  }
];

export default products;
