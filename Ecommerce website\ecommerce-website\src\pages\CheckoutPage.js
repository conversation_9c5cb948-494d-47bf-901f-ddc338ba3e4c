import React, { useState, useContext, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { CartContext } from '../context/CartContext';
import { AuthContext } from '../context/AuthContext';
import { OrderContext } from '../context/OrderContext';
import cities from '../data/cities';
import states from '../data/states';

const CheckoutPage = () => {
  const navigate = useNavigate();
  const { cartItems, getCartTotal, clearCart } = useContext(CartContext);
  const { currentUser } = useContext(AuthContext);
  const { createOrder } = useContext(OrderContext);

  // Form state
  const [formData, setFormData] = useState({
    firstName: currentUser?.name?.split(' ')[0] || '',
    lastName: currentUser?.name?.split(' ')[1] || '',
    email: currentUser?.email || '',
    address: '',
    city: '',
    state: '',
    zipCode: '',
    country: 'Pakistan',
    paymentMethod: 'credit',
    cardName: '',
    cardNumber: '',
    expMonth: '',
    expYear: '',
    cvv: ''
  });

  // Validation state
  const [errors, setErrors] = useState({});

  // Step state (1: Shipping, 2: Payment, 3: Review)
  const [step, setStep] = useState(1);

  // Available cities and states based on selected country
  const [availableCities, setAvailableCities] = useState(cities["Pakistan"] || []);
  const [availableStates, setAvailableStates] = useState(states["Pakistan"] || []);

  // Calculate order summary
  const subtotal = getCartTotal();
  const shipping = subtotal > 100 ? 0 : 10;
  const tax = subtotal * 0.08;
  const total = subtotal + shipping + tax;

  // Update available cities and states when country changes
  useEffect(() => {
    if (formData.country) {
      // Update cities
      if (cities[formData.country]) {
        setAvailableCities(cities[formData.country]);
      }

      // Update states
      if (states[formData.country]) {
        setAvailableStates(states[formData.country]);
      }

      // Reset city and state when country changes
      setFormData(prev => ({
        ...prev,
        city: '',
        state: ''
      }));
    }
  }, [formData.country]);

  // Handle form input changes
  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));

    // Clear error when field is edited
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: null
      }));
    }
  };

  // Validate form based on current step
  const validateForm = () => {
    const newErrors = {};

    if (step === 1) {
      // Shipping validation
      if (!formData.firstName.trim()) newErrors.firstName = 'First name is required';
      if (!formData.lastName.trim()) newErrors.lastName = 'Last name is required';
      if (!formData.email.trim()) newErrors.email = 'Email is required';
      if (!/^\S+@\S+\.\S+$/.test(formData.email)) newErrors.email = 'Email is invalid';
      if (!formData.address.trim()) newErrors.address = 'Address is required';
      if (!formData.city.trim()) newErrors.city = 'City is required';
      if (!formData.state.trim()) newErrors.state = 'State is required';
      if (!formData.zipCode.trim()) newErrors.zipCode = 'ZIP code is required';
    } else if (step === 2) {
      // Payment validation
      if (formData.paymentMethod === 'credit') {
        if (!formData.cardName.trim()) newErrors.cardName = 'Name on card is required';
        if (!formData.cardNumber.trim()) newErrors.cardNumber = 'Card number is required';
        if (!/^\d{16}$/.test(formData.cardNumber.replace(/\s/g, ''))) {
          newErrors.cardNumber = 'Card number must be 16 digits';
        }
        if (!formData.expMonth.trim()) newErrors.expMonth = 'Expiration month is required';
        if (!formData.expYear.trim()) newErrors.expYear = 'Expiration year is required';
        if (!formData.cvv.trim()) newErrors.cvv = 'CVV is required';
        if (!/^\d{3,4}$/.test(formData.cvv)) newErrors.cvv = 'CVV must be 3 or 4 digits';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle next step
  const handleNextStep = () => {
    if (validateForm()) {
      setStep(prev => prev + 1);
    }
  };

  // Handle previous step
  const handlePrevStep = () => {
    setStep(prev => prev - 1);
  };

  // Handle form submission
  const handleSubmit = (e) => {
    e.preventDefault();

    if (validateForm()) {
      // Create the order with customer information and cart items
      createOrder({
        ...formData,
        items: cartItems
      });

      // Clear the cart and navigate to order confirmation
      setTimeout(() => {
        clearCart();
        navigate('/order-confirmation');
      }, 1500);
    }
  };

  // If cart is empty, redirect to cart page
  if (cartItems.length === 0) {
    return (
      <div className="container py-5 text-center">
        <h1 className="mb-4">Checkout</h1>
        <div className="card p-5">
          <h3>Your cart is empty</h3>
          <p>You need to add items to your cart before checking out.</p>
          <button
            className="btn btn-primary"
            onClick={() => navigate('/products')}
          >
            Browse Products
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="container py-5">
      <h1 className="mb-4">Checkout</h1>

      {/* Checkout Progress */}
      <div className="row mb-4">
        <div className="col-12">
          <div className="progress" style={{ height: '4px' }}>
            <div
              className="progress-bar"
              role="progressbar"
              style={{ width: `${(step / 3) * 100}%` }}
              aria-valuenow={(step / 3) * 100}
              aria-valuemin="0"
              aria-valuemax="100"
            ></div>
          </div>
          <div className="d-flex justify-content-between mt-2">
            <div className={`text-center ${step >= 1 ? 'text-primary' : ''}`}>
              <div className={`rounded-circle d-inline-block ${step >= 1 ? 'bg-primary' : 'bg-secondary'}`} style={{ width: '25px', height: '25px' }}></div>
              <div>Shipping</div>
            </div>
            <div className={`text-center ${step >= 2 ? 'text-primary' : ''}`}>
              <div className={`rounded-circle d-inline-block ${step >= 2 ? 'bg-primary' : 'bg-secondary'}`} style={{ width: '25px', height: '25px' }}></div>
              <div>Payment</div>
            </div>
            <div className={`text-center ${step >= 3 ? 'text-primary' : ''}`}>
              <div className={`rounded-circle d-inline-block ${step >= 3 ? 'bg-primary' : 'bg-secondary'}`} style={{ width: '25px', height: '25px' }}></div>
              <div>Review</div>
            </div>
          </div>
        </div>
      </div>

      <div className="row">
        {/* Checkout Form */}
        <div className="col-lg-8">
          <div className="card mb-4">
            <div className="card-body">
              <form onSubmit={handleSubmit}>
                {/* Step 1: Shipping Information */}
                {step === 1 && (
                  <div>
                    <h5 className="card-title mb-4">Shipping Information</h5>

                    <div className="row">
                      <div className="col-md-6 mb-3">
                        <label htmlFor="firstName" className="form-label">First Name</label>
                        <input
                          type="text"
                          className={`form-control ${errors.firstName ? 'is-invalid' : ''}`}
                          id="firstName"
                          name="firstName"
                          value={formData.firstName}
                          onChange={handleChange}
                        />
                        {errors.firstName && <div className="invalid-feedback">{errors.firstName}</div>}
                      </div>

                      <div className="col-md-6 mb-3">
                        <label htmlFor="lastName" className="form-label">Last Name</label>
                        <input
                          type="text"
                          className={`form-control ${errors.lastName ? 'is-invalid' : ''}`}
                          id="lastName"
                          name="lastName"
                          value={formData.lastName}
                          onChange={handleChange}
                        />
                        {errors.lastName && <div className="invalid-feedback">{errors.lastName}</div>}
                      </div>
                    </div>

                    <div className="mb-3">
                      <label htmlFor="email" className="form-label">Email</label>
                      <input
                        type="email"
                        className={`form-control ${errors.email ? 'is-invalid' : ''}`}
                        id="email"
                        name="email"
                        value={formData.email}
                        onChange={handleChange}
                      />
                      {errors.email && <div className="invalid-feedback">{errors.email}</div>}
                    </div>

                    <div className="mb-3">
                      <label htmlFor="address" className="form-label">Address</label>
                      <input
                        type="text"
                        className={`form-control ${errors.address ? 'is-invalid' : ''}`}
                        id="address"
                        name="address"
                        value={formData.address}
                        onChange={handleChange}
                      />
                      {errors.address && <div className="invalid-feedback">{errors.address}</div>}
                    </div>

                    <div className="row">
                      <div className="col-md-5 mb-3">
                        <label htmlFor="city" className="form-label">City</label>
                        <select
                          className={`form-select ${errors.city ? 'is-invalid' : ''}`}
                          id="city"
                          name="city"
                          value={formData.city}
                          onChange={handleChange}
                        >
                          <option value="">Select City</option>
                          {availableCities.map((city, index) => (
                            <option key={index} value={city}>{city}</option>
                          ))}
                        </select>
                        {errors.city && <div className="invalid-feedback">{errors.city}</div>}
                      </div>

                      <div className="col-md-4 mb-3">
                        <label htmlFor="state" className="form-label">State/Province</label>
                        <select
                          className={`form-select ${errors.state ? 'is-invalid' : ''}`}
                          id="state"
                          name="state"
                          value={formData.state}
                          onChange={handleChange}
                        >
                          <option value="">Select State/Province</option>
                          {availableStates.map((state, index) => (
                            <option key={index} value={state}>{state}</option>
                          ))}
                        </select>
                        {errors.state && <div className="invalid-feedback">{errors.state}</div>}
                      </div>

                      <div className="col-md-3 mb-3">
                        <label htmlFor="zipCode" className="form-label">ZIP Code</label>
                        <input
                          type="text"
                          className={`form-control ${errors.zipCode ? 'is-invalid' : ''}`}
                          id="zipCode"
                          name="zipCode"
                          value={formData.zipCode}
                          onChange={handleChange}
                        />
                        {errors.zipCode && <div className="invalid-feedback">{errors.zipCode}</div>}
                      </div>
                    </div>

                    <div className="mb-3">
                      <label htmlFor="country" className="form-label">Country</label>
                      <select
                        className="form-select"
                        id="country"
                        name="country"
                        value={formData.country}
                        onChange={handleChange}
                      >
                        <option value="Pakistan">Pakistan</option>
                        <option value="United States">United States</option>
                        <option value="Canada">Canada</option>
                        <option value="United Kingdom">United Kingdom</option>
                        <option value="Australia">Australia</option>
                      </select>
                    </div>

                    <div className="d-flex justify-content-end mt-4">
                      <button
                        type="button"
                        className="btn btn-primary"
                        onClick={handleNextStep}
                      >
                        Continue to Payment
                      </button>
                    </div>
                  </div>
                )}

                {/* Step 2: Payment Information */}
                {step === 2 && (
                  <div>
                    <h5 className="card-title mb-4">Payment Information</h5>

                    <div className="mb-4">
                      <div className="form-check mb-2">
                        <input
                          className="form-check-input"
                          type="radio"
                          name="paymentMethod"
                          id="credit"
                          value="credit"
                          checked={formData.paymentMethod === 'credit'}
                          onChange={handleChange}
                        />
                        <label className="form-check-label" htmlFor="credit">
                          <i className="far fa-credit-card me-2"></i>Credit Card
                        </label>
                      </div>
                      <div className="form-check mb-2">
                        <input
                          className="form-check-input"
                          type="radio"
                          name="paymentMethod"
                          id="paypal"
                          value="paypal"
                          checked={formData.paymentMethod === 'paypal'}
                          onChange={handleChange}
                        />
                        <label className="form-check-label" htmlFor="paypal">
                          <i className="fab fa-paypal me-2"></i>PayPal
                        </label>
                      </div>
                      <div className="form-check mb-2">
                        <input
                          className="form-check-input"
                          type="radio"
                          name="paymentMethod"
                          id="cod"
                          value="cod"
                          checked={formData.paymentMethod === 'cod'}
                          onChange={handleChange}
                        />
                        <label className="form-check-label" htmlFor="cod">
                          <i className="fas fa-money-bill-wave me-2"></i>Cash on Delivery
                        </label>
                      </div>
                    </div>

                    {formData.paymentMethod === 'credit' && (
                      <div>
                        <div className="mb-3">
                          <label htmlFor="cardName" className="form-label">Name on Card</label>
                          <input
                            type="text"
                            className={`form-control ${errors.cardName ? 'is-invalid' : ''}`}
                            id="cardName"
                            name="cardName"
                            value={formData.cardName}
                            onChange={handleChange}
                          />
                          {errors.cardName && <div className="invalid-feedback">{errors.cardName}</div>}
                          <small className="text-muted">Full name as displayed on card</small>
                        </div>

                        <div className="mb-3">
                          <label htmlFor="cardNumber" className="form-label">Card Number</label>
                          <input
                            type="text"
                            className={`form-control ${errors.cardNumber ? 'is-invalid' : ''}`}
                            id="cardNumber"
                            name="cardNumber"
                            value={formData.cardNumber}
                            onChange={handleChange}
                            placeholder="XXXX XXXX XXXX XXXX"
                          />
                          {errors.cardNumber && <div className="invalid-feedback">{errors.cardNumber}</div>}
                        </div>

                        <div className="row">
                          <div className="col-md-4 mb-3">
                            <label htmlFor="expMonth" className="form-label">Expiration Month</label>
                            <select
                              className={`form-select ${errors.expMonth ? 'is-invalid' : ''}`}
                              id="expMonth"
                              name="expMonth"
                              value={formData.expMonth}
                              onChange={handleChange}
                            >
                              <option value="">Month</option>
                              {Array.from({ length: 12 }, (_, i) => i + 1).map(month => (
                                <option key={month} value={month}>{month}</option>
                              ))}
                            </select>
                            {errors.expMonth && <div className="invalid-feedback">{errors.expMonth}</div>}
                          </div>

                          <div className="col-md-4 mb-3">
                            <label htmlFor="expYear" className="form-label">Expiration Year</label>
                            <select
                              className={`form-select ${errors.expYear ? 'is-invalid' : ''}`}
                              id="expYear"
                              name="expYear"
                              value={formData.expYear}
                              onChange={handleChange}
                            >
                              <option value="">Year</option>
                              {Array.from({ length: 10 }, (_, i) => new Date().getFullYear() + i).map(year => (
                                <option key={year} value={year}>{year}</option>
                              ))}
                            </select>
                            {errors.expYear && <div className="invalid-feedback">{errors.expYear}</div>}
                          </div>

                          <div className="col-md-4 mb-3">
                            <label htmlFor="cvv" className="form-label">CVV</label>
                            <input
                              type="text"
                              className={`form-control ${errors.cvv ? 'is-invalid' : ''}`}
                              id="cvv"
                              name="cvv"
                              value={formData.cvv}
                              onChange={handleChange}
                              placeholder="XXX"
                            />
                            {errors.cvv && <div className="invalid-feedback">{errors.cvv}</div>}
                          </div>
                        </div>
                      </div>
                    )}

                    {formData.paymentMethod === 'paypal' && (
                      <div className="alert alert-info">
                        <i className="fas fa-info-circle me-2"></i>
                        You will be redirected to PayPal to complete your payment after reviewing your order.
                      </div>
                    )}

                    {formData.paymentMethod === 'cod' && (
                      <div className="alert alert-info">
                        <i className="fas fa-info-circle me-2"></i>
                        You will pay in cash when your order is delivered. Please have the exact amount ready.
                        <div className="mt-2">
                          <strong>Note:</strong> Cash on Delivery is only available in selected areas of Pakistan.
                        </div>
                      </div>
                    )}

                    <div className="d-flex justify-content-between mt-4">
                      <button
                        type="button"
                        className="btn btn-outline-secondary"
                        onClick={handlePrevStep}
                      >
                        Back to Shipping
                      </button>
                      <button
                        type="button"
                        className="btn btn-primary"
                        onClick={handleNextStep}
                      >
                        Review Order
                      </button>
                    </div>
                  </div>
                )}

                {/* Step 3: Review Order */}
                {step === 3 && (
                  <div>
                    <h5 className="card-title mb-4">Review Your Order</h5>

                    <div className="mb-4">
                      <h6>Shipping Information</h6>
                      <p className="mb-1">
                        {formData.firstName} {formData.lastName}
                      </p>
                      <p className="mb-1">{formData.address}</p>
                      <p className="mb-1">
                        {formData.city}, {formData.state} {formData.zipCode}
                      </p>
                      <p className="mb-1">{formData.country}</p>
                      <p className="mb-1">{formData.email}</p>
                    </div>

                    <div className="mb-4">
                      <h6>Payment Method</h6>
                      {formData.paymentMethod === 'credit' ? (
                        <p className="mb-1">
                          <i className="far fa-credit-card me-2"></i>
                          Credit Card ending in {formData.cardNumber.slice(-4)}
                        </p>
                      ) : formData.paymentMethod === 'paypal' ? (
                        <p className="mb-1">
                          <i className="fab fa-paypal me-2"></i>
                          PayPal
                        </p>
                      ) : (
                        <p className="mb-1">
                          <i className="fas fa-money-bill-wave me-2"></i>
                          Cash on Delivery
                        </p>
                      )}
                    </div>

                    <div className="mb-4">
                      <h6>Order Items</h6>
                      {cartItems.map(item => (
                        <div key={item.id} className="d-flex justify-content-between align-items-center mb-2">
                          <div className="d-flex align-items-center">
                            <img
                              src={item.image}
                              alt={item.name}
                              className="img-fluid rounded"
                              style={{ width: '50px', height: '50px', objectFit: 'cover' }}
                            />
                            <div className="ms-3">
                              <h6 className="mb-0">{item.name}</h6>
                              <small className="text-muted">Qty: {item.quantity}</small>
                            </div>
                          </div>
                          <span>${(item.price * item.quantity).toFixed(2)}</span>
                        </div>
                      ))}
                    </div>

                    <div className="d-flex justify-content-between mt-4">
                      <button
                        type="button"
                        className="btn btn-outline-secondary"
                        onClick={handlePrevStep}
                      >
                        Back to Payment
                      </button>
                      <button
                        type="submit"
                        className="btn btn-primary"
                      >
                        Place Order
                      </button>
                    </div>
                  </div>
                )}
              </form>
            </div>
          </div>
        </div>

        {/* Order Summary */}
        <div className="col-lg-4">
          <div className="card">
            <div className="card-body">
              <h5 className="card-title mb-4">Order Summary</h5>

              {cartItems.map(item => (
                <div key={item.id} className="d-flex justify-content-between mb-2">
                  <span>{item.name} × {item.quantity}</span>
                  <span>${(item.price * item.quantity).toFixed(2)}</span>
                </div>
              ))}

              <hr />

              <div className="d-flex justify-content-between mb-2">
                <span>Subtotal</span>
                <span>${subtotal.toFixed(2)}</span>
              </div>

              <div className="d-flex justify-content-between mb-2">
                <span>Shipping</span>
                <span>{shipping === 0 ? 'Free' : `$${shipping.toFixed(2)}`}</span>
              </div>

              <div className="d-flex justify-content-between mb-2">
                <span>Tax (8%)</span>
                <span>${tax.toFixed(2)}</span>
              </div>

              <hr />

              <div className="d-flex justify-content-between mb-4">
                <strong>Total</strong>
                <strong>${total.toFixed(2)}</strong>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CheckoutPage;
