import React, { createContext, useState, useEffect } from 'react';

export const AuthContext = createContext();

export const AuthProvider = ({ children }) => {
  const [currentUser, setCurrentUser] = useState(() => {
    // Get user from local storage if available
    const savedUser = localStorage.getItem('user');
    return savedUser ? JSON.parse(savedUser) : null;
  });
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);

  // Save user to local storage whenever it changes
  useEffect(() => {
    if (currentUser) {
      localStorage.setItem('user', JSON.stringify(currentUser));
    } else {
      localStorage.removeItem('user');
    }
  }, [currentUser]);

  // Login function
  const login = async (email, password) => {
    setIsLoading(true);
    setError(null);
    
    try {
      // In a real app, you would make an API call here
      // For demo purposes, we'll simulate a successful login
      if (email === '<EMAIL>' && password === 'password') {
        const user = {
          id: 1,
          name: '<PERSON>',
          email: email,
          // Add other user details as needed
        };
        
        setCurrentUser(user);
        setIsLoading(false);
        return user;
      } else {
        throw new Error('Invalid email or password');
      }
    } catch (error) {
      setError(error.message);
      setIsLoading(false);
      throw error;
    }
  };

  // Register function
  const register = async (name, email, password) => {
    setIsLoading(true);
    setError(null);
    
    try {
      // In a real app, you would make an API call here
      // For demo purposes, we'll simulate a successful registration
      const user = {
        id: Date.now(), // Generate a unique ID
        name,
        email,
        // Add other user details as needed
      };
      
      setCurrentUser(user);
      setIsLoading(false);
      return user;
    } catch (error) {
      setError(error.message);
      setIsLoading(false);
      throw error;
    }
  };

  // Logout function
  const logout = () => {
    setCurrentUser(null);
  };

  return (
    <AuthContext.Provider value={{
      currentUser,
      isLoading,
      error,
      login,
      register,
      logout
    }}>
      {children}
    </AuthContext.Provider>
  );
};
