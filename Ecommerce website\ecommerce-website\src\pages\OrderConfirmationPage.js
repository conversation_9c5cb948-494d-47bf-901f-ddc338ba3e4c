import React, { useContext } from 'react';
import { Link, Navigate } from 'react-router-dom';
import { OrderContext } from '../context/OrderContext';

const OrderConfirmationPage = () => {
  // Get order information from context
  const { orderInfo } = useContext(OrderContext);

  // If no order info exists, redirect to home
  if (!orderInfo.orderNumber) {
    return <Navigate to="/" />;
  }

  // Extract order details
  const {
    orderNumber,
    orderDate,
    estimatedDelivery,
    customerInfo,
    paymentMethod,
    cardDetails,
    items: orderItems,
    subtotal,
    shipping,
    tax,
    total
  } = orderInfo;

  // Format date for display
  const formatDate = (date) => {
    return date.toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <div className="container py-5">
      <div className="card border-0 shadow-lg">
        <div className="card-header bg-success text-white p-4">
          <div className="d-flex align-items-center">
            <div className="rounded-circle bg-white p-3 me-3">
              <i className="fas fa-check text-success" style={{ fontSize: '2rem' }}></i>
            </div>
            <div>
              <h3 className="mb-0">Order Confirmed!</h3>
              <p className="mb-0">Thank you for your purchase</p>
            </div>
          </div>
        </div>

        <div className="card-body p-4">
          <div className="row mb-4">
            <div className="col-md-6">
              <div className="mb-4">
                <h5 className="text-primary mb-3">Order Information</h5>
                <p className="mb-1"><strong>Order Number:</strong> #{orderNumber}</p>
                <p className="mb-1"><strong>Order Date:</strong> {formatDate(orderDate)}</p>
                <p className="mb-1">
                  <strong>Payment Method:</strong> {
                    paymentMethod === 'credit' ? 'Credit Card' :
                    paymentMethod === 'paypal' ? 'PayPal' :
                    'Cash on Delivery'
                  }
                </p>
                <p className="mb-1"><strong>Shipping Method:</strong> Standard Delivery</p>
              </div>

              <div>
                <h5 className="text-primary mb-3">Shipping Address</h5>
                <p className="mb-1">{customerInfo.firstName} {customerInfo.lastName}</p>
                <p className="mb-1">{customerInfo.address}</p>
                <p className="mb-1">{customerInfo.city}, {customerInfo.state} {customerInfo.zipCode}</p>
                <p className="mb-1">{customerInfo.country}</p>
              </div>
            </div>

            <div className="col-md-6">
              <div className="mb-4">
                <h5 className="text-primary mb-3">Delivery Information</h5>
                <div className="d-flex align-items-center mb-3">
                  <div className="bg-primary text-white rounded-circle p-3 me-3">
                    <i className="fas fa-truck"></i>
                  </div>
                  <div>
                    <p className="mb-0"><strong>Estimated Delivery:</strong></p>
                    <p className="mb-0">{formatDate(estimatedDelivery)}</p>
                  </div>
                </div>

                <div className="progress mb-3" style={{ height: '5px' }}>
                  <div className="progress-bar bg-success" role="progressbar" style={{ width: '25%' }}></div>
                </div>

                <div className="d-flex justify-content-between text-center">
                  <div>
                    <div className="rounded-circle bg-success text-white d-inline-flex align-items-center justify-content-center mb-2" style={{ width: '30px', height: '30px' }}>
                      <i className="fas fa-check"></i>
                    </div>
                    <p className="small mb-0">Order Placed</p>
                  </div>
                  <div>
                    <div className="rounded-circle bg-secondary text-white d-inline-flex align-items-center justify-content-center mb-2" style={{ width: '30px', height: '30px' }}>
                      <i className="fas fa-box"></i>
                    </div>
                    <p className="small mb-0">Processing</p>
                  </div>
                  <div>
                    <div className="rounded-circle bg-secondary text-white d-inline-flex align-items-center justify-content-center mb-2" style={{ width: '30px', height: '30px' }}>
                      <i className="fas fa-shipping-fast"></i>
                    </div>
                    <p className="small mb-0">Shipped</p>
                  </div>
                  <div>
                    <div className="rounded-circle bg-secondary text-white d-inline-flex align-items-center justify-content-center mb-2" style={{ width: '30px', height: '30px' }}>
                      <i className="fas fa-home"></i>
                    </div>
                    <p className="small mb-0">Delivered</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <h5 className="text-primary mb-3">Order Summary</h5>
          <div className="table-responsive mb-4">
            <table className="table">
              <thead className="table-light">
                <tr>
                  <th>Product</th>
                  <th>Price</th>
                  <th>Quantity</th>
                  <th className="text-end">Total</th>
                </tr>
              </thead>
              <tbody>
                {orderItems.map(item => (
                  <tr key={item.id}>
                    <td>
                      <div className="d-flex align-items-center">
                        <img
                          src={item.image}
                          alt={item.name}
                          className="img-fluid rounded"
                          style={{ width: '50px', height: '50px', objectFit: 'cover' }}
                          onError={(e) => e.target.src = `https://via.placeholder.com/50x50?text=${encodeURIComponent(item.name)}`}
                        />
                        <div className="ms-3">
                          <p className="mb-0">{item.name}</p>
                        </div>
                      </div>
                    </td>
                    <td>${item.price.toFixed(2)}</td>
                    <td>{item.quantity}</td>
                    <td className="text-end">${(item.price * item.quantity).toFixed(2)}</td>
                  </tr>
                ))}
              </tbody>
              <tfoot>
                <tr>
                  <td colSpan="3" className="text-end">Subtotal:</td>
                  <td className="text-end">${subtotal.toFixed(2)}</td>
                </tr>
                <tr>
                  <td colSpan="3" className="text-end">Shipping:</td>
                  <td className="text-end">{shipping === 0 ? 'Free' : `$${shipping.toFixed(2)}`}</td>
                </tr>
                <tr>
                  <td colSpan="3" className="text-end">Tax:</td>
                  <td className="text-end">${tax.toFixed(2)}</td>
                </tr>
                <tr>
                  <td colSpan="3" className="text-end"><strong>Total:</strong></td>
                  <td className="text-end"><strong>${total.toFixed(2)}</strong></td>
                </tr>
              </tfoot>
            </table>
          </div>

          <div className="alert alert-info d-flex align-items-center" role="alert">
            <i className="fas fa-info-circle me-3 fs-4"></i>
            <div>
              We've sent a confirmation email to {customerInfo.email} with all the details of your order.
              You can also track your order status in your account.
            </div>
          </div>

          <div className="d-flex justify-content-between mt-4">
            <Link to="/" className="btn btn-primary">
              <i className="fas fa-shopping-cart me-2"></i>Continue Shopping
            </Link>
            <Link to="/orders" className="btn btn-outline-primary">
              <i className="fas fa-list me-2"></i>View All Orders
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};

export default OrderConfirmationPage;
